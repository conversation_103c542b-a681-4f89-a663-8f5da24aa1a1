local LingGen = {}
local Variables = require("Server.Modules.Variables")
local Utils = require("Server.Modules.Utils")

-- 初始化灵根系统
function LingGen.Init()
    _P("[LingGen] 初始化灵根系统...")

    -- 注册事件监听灵根相关状态
    Ext.Osiris.RegisterListener("StatusApplied", 4, "after", LingGen.OnStatusApplied_after)

    -- 注册事件监听灵根相关洗点
    Ext.Osiris.RegisterListener("RespecCompleted", 1, "after", LingGen.OnRespecCompleted_after)

    _P("[LingGen] 灵根系统初始化完成！")
end

--觉醒异灵根(火a 土b 金c 水d 木e)
function LingGen.ApplyYiLingGen(Object,a,b,c,d,e)
    if 4*b == d and b ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_BING', -1)
    end
    if 2*e == 3*a and e ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_XUE', -1)
    end
    if c == 4*d and c ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_LEI', -1)
    end
    if 3*e == 2*b and b ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_FENG', -1)
    end
    if a == e*2 and a == b*6 and a ~= 0 and e ~= 0 and b ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_GUANG', -1)
    end
    if d == c*2 and d == e*6 and d ~= 0 and c ~= 0 and e ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_AN', -1)
    end
    if e == a*3 and e == d*3 and e ~= 0 and a ~= 0 and d ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_DU', -1)
    end
    if a == b and a == c and a == d and a == e and a ~= 0 and b ~= 0 and c ~= 0 and d ~= 0 and e ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_HUNDUN', -1)
    end
    
end

--天灵根检测
function LingGen.ApplyTianLingGen_Check(Object,TLG,Table_LG)
    local RESULT = true

    _P('杂灵根检测')
    for _, LG in ipairs(Table_LG) do
        if Osi.HasActiveStatus(Object,LG) == 1 then
            _P('有杂灵根检测'..LG)
            RESULT = false
            break
        end
    end

    if RESULT == true then
        _P('添加天灵根') --DEBUG
        Osi.ApplyStatus(Object, TLG.."_TIAN", -1, 1,Object)
    end
    
end

--异灵根检测
function LingGen.ApplyYiLingGen_Check(Object)

    _P('异灵根检测')
    local a,b,c,d,e,RESULT_LG = Utils.Get.LingGen(Object)
    if 4*b == d and b ~= 0  then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_BING', -1)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_BING',{'BANXIAN_LG_H','BANXIAN_LG_J','BANXIAN_LG_M'})
    end
    if 2*e == 3*a and e ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_XUE', -1)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_XUE',{'BANXIAN_LG_T','BANXIAN_LG_J','BANXIAN_LG_S'})
    end
    if c == 4*d and c ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_LEI', -1)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_LEI',{'BANXIAN_LG_H','BANXIAN_LG_T','BANXIAN_LG_M'})
    end
    if 3*e == 2*b and b ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_FENG', -1)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_FENG',{'BANXIAN_LG_H','BANXIAN_LG_J','BANXIAN_LG_S'})
    end
    if a == e*2 and a == b*6 and a ~= 0 and e ~= 0 and b ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_GUANG', -1)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_GUANG',{'BANXIAN_LG_J','BANXIAN_LG_S'})
    end
    if d == c*2 and d == e*6 and d ~= 0 and c ~= 0 and e ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_AN', -1)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_AN',{'BANXIAN_LG_H','BANXIAN_LG_T'})
    end
    if e == a*3 and e == d*3 and e ~= 0 and a ~= 0 and d ~= 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_DU', -1)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_DU',{'T','J'})
    end
    if a == b and a == c and a == d and a == e and a ~= 0 and b ~= 0 and c ~= 0 and d ~= 0 and e ~= 0 and Osi.HasActiveStatus(Object,'BANXIAN_LG_HUNDUN') == 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_HUNDUN', -1)
    end
    
end

--主灵根检测
function LingGen.ApplyTopLingGen_Check(Object)

    _P('主灵根检测')
    local a,b,c,d,e,RESULT_LG = Utils.Get.LingGen(Object)
    if math.max(a,b,c,d,e) == a and a > 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_HUO', -1, 1,Object)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_HUO',{'BANXIAN_LG_M','BANXIAN_LG_T','BANXIAN_LG_J','BANXIAN_LG_S'})
    end
    if math.max(a,b,c,d,e) == b and b > 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_TU', -1, 1,Object)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_TU',{'BANXIAN_LG_H','BANXIAN_LG_M','BANXIAN_LG_J','BANXIAN_LG_S'})
    end
    if math.max(a,b,c,d,e) == c and c > 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_JIN', -1, 1,Object)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_JIN',{'BANXIAN_LG_H','BANXIAN_LG_T','BANXIAN_LG_M','BANXIAN_LG_S'})
    end
    if math.max(a,b,c,d,e) == d and d > 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_SHUI', -1, 1,Object)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_SHUI',{'BANXIAN_LG_H','BANXIAN_LG_T','BANXIAN_LG_J','BANXIAN_LG_M'})
    end
    if math.max(a,b,c,d,e) == e and e > 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_MU', -1, 1,Object)
        LingGen.ApplyTianLingGen_Check(Object,'BANXIAN_LG_MU',{'BANXIAN_LG_H','BANXIAN_LG_T','BANXIAN_LG_J','BANXIAN_LG_S'})
    end
    
end

--获取角色参数
function LingGen.GetCharacterParams(Object,a,b,c,d,e,r,TZ)
    if string.find(Object, 'Astarion') then
        a,b,c,d,e,r,TZ = 10,5,15,5,15,50,2
    elseif string.find(Object, 'Laezel') then
        a,b,c,d,e,r,TZ = 10,10,60,10,10,100,1
    elseif string.find(Object, 'Gale') then
        a,b,c,d,e,r,TZ = 4,4,4,4,4,20,5
    elseif string.find(Object, 'Shadowheart') then
        a,b,c,d,e,r,TZ = 20,20,25,25,10,100,1
    elseif string.find(Object, 'Wyll') then
        a,b,c,d,e,r,TZ = 0,20,30,0,0,50,2
    elseif string.find(Object, 'Jaheira') then
        a,b,c,d,e,r,TZ = 0,20,0,30,50,100,1
    elseif string.find(Object, 'Minthara') then
        a,b,c,d,e,r,TZ = 30,0,5,15,0,50,2
    elseif string.find(Object, 'Minsc') then
        a,b,c,d,e,r,TZ = 10,80,10,0,0,100,1
    elseif string.find(Object, 'Halsin') then
        a,b,c,d,e,r,TZ = 0,0,0,0,50,50,2
    elseif string.find(Object, 'Alfira') then
        a,b,c,d,e,r,TZ = 2,3,2,3,12,20,3
    elseif string.find(Object, 'Losiir') then
        a,b,c,d,e,r,TZ = 5,15,50,20,10,100,1
    elseif Osi.IsTagged(Object, 'fe825e69-1569-471f-9b3f-28fd3b929683') == 1 then
        a,b,c,d,e,r,TZ = 8,0,0,1,11,r,TZ
    end
    return a,b,c,d,e,r,TZ
end

--觉醒灵根
function LingGen.Add_First(Object)
    -- 五行灵根对应表（金->c,木->e,水->d,火->a,土->b）
    local lg = { a = 0, b = 0, c = 0, d = 0, e = 0 }

    -- 获取随机灵根资质
    local r,TZ = Utils.LingGen.Random(1)

    -- 先天资质覆盖
    if Osi.HasPassive(Object, 'BanXian_LingGen_T0') == 1 then
        r,TZ = 10,math.random(6,10)
    elseif Osi.HasPassive(Object, 'BanXian_LingGen_T1') == 1 then
        r,TZ = 20,math.random(3,5)
    elseif Osi.HasPassive(Object, 'BanXian_LingGen_T2') == 1 then
        r,TZ = 50,2
    elseif Osi.HasPassive(Object, 'BanXian_LingGen_T3') == 1 then
        r,TZ = 100,1
    elseif Osi.HasPassive(Object, 'BanXian_LingGen_NIL') == 1 then
        r,TZ = 0,0
    else
        _P('随机灵根资质')
    end

    -- 强制设置天雷灵根和天血灵根配置
    -- 天雷灵根条件：金 = 4×水 (c = 4*d)
    -- 天血灵根条件：2×木 = 3×火 (2*e = 3*a)
    -- 根据你的需求：雷灵根=金：水=4：1，血灵根=木：火=3：2

    -- 设置基础比例，确保同时满足两个异灵根条件
    local base_water = 5    -- 水灵根基础值
    local base_gold = base_water * 4    -- 金灵根 = 4×水 (满足雷灵根条件)
    local base_fire = 10    -- 火灵根基础值
    local base_wood = (base_fire * 3) / 2    -- 木灵根 = 3×火/2 (满足血灵根条件)

    -- 设置灵根值
    lg.a = base_fire    -- 火
    lg.b = 0           -- 土 (设为0确保天灵根)
    lg.c = base_gold   -- 金
    lg.d = base_water  -- 水
    lg.e = base_wood   -- 木

    -- 调整资质等级以匹配异灵根要求
    r = 20  -- 设置为异灵根等级
    TZ = math.random(7,9)  -- 对应的资质点

    _P('强制觉醒天雷灵根和天血灵根')
    _P('灵根配比 - 火:'..lg.a..' 土:'..lg.b..' 金:'..lg.c..' 水:'..lg.d..' 木:'..lg.e)
    _P('雷灵根检查 - 金/水='..lg.c..'/'..lg.d..'='..lg.c/lg.d..' (需要4)')
    _P('血灵根检查 - 木/火='..lg.e..'/'..lg.a..'='..lg.e/lg.a..' (需要1.5)')

    -- 原有的灵根分配策略已被替换为固定配置
    -- 如果需要保留随机性，可以在上述固定值基础上添加小幅随机调整

    -- 保留后续处理逻辑不变
    lg.a,lg.b,lg.c,lg.d,lg.e,r,TZ = LingGen.GetCharacterParams(Object,lg.a,lg.b,lg.c,lg.d,lg.e,r,TZ)
    LingGen.ApplyYiLingGen(Object,lg.a,lg.b,lg.c,lg.d,lg.e)


    --觉醒灵根
    if r == 100 then
        if Osi.HasPassive(Object,'BanXian_LingGen_T3') == 0 then
            Osi.AddPassive(Object, 'BanXian_LingGen_T3')
            Ext.Utils.Print('觉醒灵根资质[平平无奇]:'..TZ..'资质点')
        end
    elseif r == 50 then
        if Osi.HasPassive(Object,'BanXian_LingGen_T2') == 0 then
            Osi.AddPassive(Object, 'BanXian_LingGen_T2')
            Ext.Utils.Print('觉醒灵根资质[仙天慧根]:'..TZ..'资质点')
        end
    elseif r == 20 then
        if Osi.HasPassive(Object,'BanXian_LingGen_T1') == 0 then
            Osi.AddPassive(Object, 'BanXian_LingGen_T1')
            Ext.Utils.Print('觉醒灵根资质[大帝之资]:'..TZ..'资质点')
        end
    elseif r == 10 then
        if Osi.HasPassive(Object,'BanXian_LingGen_T0') == 0 then
            Osi.AddPassive(Object, 'BanXian_LingGen_T0')
            Ext.Utils.Print('觉醒灵根资质[先天道体]:'..TZ..'资质点')
        end
    end

    --资质点分配
    Osi.ApplyStatus(Object,'BANXIAN_LG_TZ', TZ*6)

    if lg.a > 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_H', lg.a*6)
        Osi.ApplyStatus(Object, 'BANXIAN_LG_HT', lg.a*6+lg.b*6)
        Osi.ApplyStatus(Object, 'BANXIAN_LG_HS', lg.a*6+lg.d*6)
        Ext.Utils.Print('觉醒灵根[火]:'..lg.a..'/'..r)
    end
    if lg.b > 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_T', lg.b*6)
        Osi.ApplyStatus(Object, 'BANXIAN_LG_TJ', lg.b*6+lg.c*6)
        Osi.ApplyStatus(Object, 'BANXIAN_LG_TM', lg.b*6+lg.e*6)
        Ext.Utils.Print('觉醒灵根[土]:'..lg.b..'/'..r)
    end
    if lg.c > 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_J', lg.c*6)
        Osi.ApplyStatus(Object, 'BANXIAN_LG_JS', lg.c*6+lg.d*6)
        Osi.ApplyStatus(Object, 'BANXIAN_LG_JH', lg.c*6+lg.a*6)
        Ext.Utils.Print('觉醒灵根[金]:'..lg.c..'/'..r)
    end
    if lg.d > 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_S', lg.d*6)
        Osi.ApplyStatus(Object, 'BANXIAN_LG_SM', lg.d*6+lg.e*6)
        Osi.ApplyStatus(Object, 'BANXIAN_LG_ST', lg.d*6+lg.b*6)
        Ext.Utils.Print('觉醒灵根[水]:'..lg.d..'/'..r)
    end
    if lg.e > 0 then
        Osi.ApplyStatus(Object, 'BANXIAN_LG_M', lg.e*6)
        Osi.ApplyStatus(Object, 'BANXIAN_LG_MH', lg.e*6+lg.a*6)
        Osi.ApplyStatus(Object, 'BANXIAN_LG_MJ', lg.e*6+lg.c*6)
        Ext.Utils.Print('觉醒灵根[木]:'..lg.e..'/'..r)
    end
    Utils.ShenShi.Check(Object)
    Utils.BanXianList_AddtoList(Object)
end

--夺取灵根
function LingGen.Take_Devastatingly(caster, target)
    local lg = { a = 0, b = 0, c = 0, d = 0, e = 0 }
    lg.a,lg.b,lg.c,lg.d,lg.e,RESULT_LG = Utils.Get.LingGen(target)

    local r = lg.a + lg.b + lg.c + lg.d + lg.e
    
    --夺取灵根配比
    Osi.AddPassive(target, 'BanXian_LingGen_NIL')
    for LG, NAME in pairs(Variables.Constants.LingGen) do

        if Osi.HasActiveStatus(target, LG) == 1 then --移除目标灵根

            Variables.Constants.LingGenNum[LG] = Osi.GetStatusTurns(target, LG)
            local turn = Variables.Constants.LingGenNum[LG]
            Osi.RemoveStatus(target, LG)

            if Osi.HasActiveStatus(caster, LG) == 1 then --移除施法者灵根
                Osi.RemoveStatus(caster, LG)
            end
            Osi.ApplyStatus(caster,LG,turn*6,1,caster)
            Variables.Constants.LingGenNum[LG] = 0 --清空临时储存

        end

    end

    --异灵根
    for LG, NAME in pairs(Variables.Constants.YiLingGen) do
        if Osi.HasActiveStatus(target, LG) == 1 then
            Osi.RemoveStatus(target, LG)
            Osi.ApplyStatus(caster, LG, -1)
        end
    end

    --分配施法者灵根配比
    if lg.a > 0 then
        ApplyStatus(caster, 'BANXIAN_LG_HT', lg.a*6+lg.b*6)
        ApplyStatus(caster, 'BANXIAN_LG_HS', lg.a*6+lg.d*6)
        Ext.Utils.Print('重铸灵根[火]:'..lg.a..'/'..r)
    end
    if lg.b > 0 then
        ApplyStatus(caster, 'BANXIAN_LG_TJ', lg.b*6+lg.c*6)
        ApplyStatus(caster, 'BANXIAN_LG_TM', lg.b*6+lg.e*6)
        Ext.Utils.Print('重铸灵根[土]:'..lg.b..'/'..r)
    end
    if lg.c > 0 then
        ApplyStatus(caster, 'BANXIAN_LG_JS', lg.c*6+lg.d*6)
        ApplyStatus(caster, 'BANXIAN_LG_JH', lg.c*6+lg.a*6)
        Ext.Utils.Print('重铸灵根[金]:'..lg.c..'/'..r)
    end
    if lg.d > 0 then
        ApplyStatus(caster, 'BANXIAN_LG_SM', lg.d*6+lg.e*6)
        ApplyStatus(caster, 'BANXIAN_LG_ST', lg.d*6+lg.b*6)
        Ext.Utils.Print('重铸灵根[水]:'..lg.d..'/'..r)
    end
    if lg.e > 0 then
        ApplyStatus(caster, 'BANXIAN_LG_MH', lg.e*6+lg.a*6)
        ApplyStatus(caster, 'BANXIAN_LG_MJ', lg.e*6+lg.c*6)
        Ext.Utils.Print('重铸灵根[木]:'..lg.e..'/'..r)
    end

end


-----------------------------------------------------------
--混沌灵根
function LingGen.HunDun_ShortRest(Object)
    local entity = Ext.Entity.Get(Object)
    _D(entity.ActionResources) --DEBUG
    
    for ResourceUUID, ResourceList in pairs(entity.ActionResources.Resources) do
        for _, Resource in ipairs(ResourceList) do -- ResourceList是GUID对应的资源列表
            -- 处理逻辑
        _D(Resource)
        local ReplenishType = Resource.ReplenishType
        for _, type in ipairs(ReplenishType) do
            if type == "Rest" then
                Resource.Amount = Resource.MaxAmount
            end
        end
        end
    end
    --entity:Replicate("ActionResources")
end

--血灵根
function LingGen.Xue_ApplyBloodCurse(Object,Causee)
    local Key = math.random(1,23)
    local Curse = Variables.Constants.LingGenXue.BloodCurse[Key]

    Osi.ApplyStatus(Object, Curse, 18, 1, Causee)
end






-- 事件·灵根状态
function LingGen.OnStatusApplied_after(Object, Status, Causee)
    if Osi.HasPassive(Object, 'BanXian_LingGen') == 0 and Osi.HasPassive(Object, 'BanXian_LingGen_NIL') == 0 then --没有觉醒过灵根
        if Status == "BIANXIAN_DAOXIN" then  --创建角色：谪仙
            _P("[EventHandlers] 事件·觉醒灵根: ") 
            PersistentVars['BXAddLingGen_Waiting'] = Object
            _P('[PersistentVars]记录数据[BXAddLingGen_Waiting]:'..Object) --DEBUG
            Osi.TimerLaunch('BanXian_AddLingGen', 12000)
        elseif Status == "POTION_OF_VITALITY" then  --活力药水觉醒
            LingGen.Add_First(Object)
            LingGen.ApplyYiLingGen_Check(Object)
            LingGen.ApplyTopLingGen_Check(Object)
        end
    elseif Status == "BANXIAN_TAKELINGGEN" then
        LingGen.Take_Devastatingly(Causee, Object)
    end

    if Status == 'SIGNAL_YLG_CHECK' then
        LingGen.ApplyYiLingGen_Check(Object)
        LingGen.ApplyTopLingGen_Check(Object)
    end

    if Status == "SIGNAL_LG_HUNDUN_SHORTREST" then
        LingGen.HunDun_ShortRest(Object) 
    end

    if Status == "BLEEDING" then
        local LG_H,LG_T,LG_J,LG_S,LG_M,RESULT = Utils.Get.LingGen(Causee)
        if 2*LG_M == 3*LG_H and LG_M ~= 0 and Osi.HasActiveStatus(Causee,'BANXIAN_LG_T') == 0 and Osi.HasActiveStatus(Causee,'BANXIAN_LG_J') == 0 and Osi.HasActiveStatus(Causee,'BANXIAN_LG_S') == 0 then
            LingGen.Xue_ApplyBloodCurse(Object,Causee)
        end
    end

end

-- 事件·灵根洗点
function LingGen.OnRespecCompleted_after(Character)

    if HasPassive(Character,'BanXian_DH_DaoXin') == 1 then
        LingGen.ApplyYiLingGen_Check(Character)
        Osi.ApplyStatus(Character, 'SIGNAL_DAOXINCHECK', 0, 1, Character)
    end

end

return LingGen
